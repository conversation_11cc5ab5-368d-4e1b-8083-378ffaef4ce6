"use client";
import {
  ThemeProvider as NextThemeProvider,
  type ThemeProviderProps,
} from "next-themes";
import type { FC } from "react";

interface IThemeProviderProps extends ThemeProviderProps {}

export const ThemeProvider: FC<IThemeProviderProps> = ({ ...props }) => {
  return (
    <NextThemeProvider
      attribute="class"
      defaultTheme="system"
      enableSystem
      disableTransitionOnChange
      storageKey="theme"
      {...props}
    />
  );
};
