import { z } from "zod";
import { db } from "@/server/db";
import { episode, podcast, season } from "@/server/db/schema";
import { createTRPCRouter, protectedOrgProcedure } from "../init";
import { randomUUID } from "crypto";

export const podcastRouter = createTRPCRouter({
  get: protectedOrgProcedure.query(async ({ ctx }) => {
    return await db.query.podcast.findMany({
      where: (p, { eq }) => eq(p.organizationId, ctx.org.id),
    });
  }),
  create: protectedOrgProcedure
    .input(
      z.object({
        name: z.string(),
        subdomain: z.string().optional(),
        description: z.string().optional(),
        image: z.string().url().optional(),
        email: z.string().email().optional(),
        owner: z.string().optional(),
        copyright: z.string().optional(),
        seasons: z.array(
          z.object({
            name: z.string(),
            description: z.string().optional(),
            image: z.string().url().optional(),
            episodes: z.array(
              z.object({
                name: z.string(),
                description: z.string().optional(),
                image: z.string().url().optional(),
                audio: z.string().url().optional(),
                duration: z.string().optional(),
              }),
            ),
          }),
        ),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const downloadImageAndStore = async (url: string | undefined) => {
        if (!url) return undefined;
        
        const uploadDir = path.join(process.cwd(), "public", "uploads");
        if (!fs.existsSync(uploadDir)) {
                fs.mkdirSync(uploadDir, { recursive: true });
              }

              const ext = path.extname(input.name) || "." + input.type.split("/")[1];
                    const filename = `${randomUUID()}${ext}`;
                    const filePath = path.join(uploadDir, filename);
              
                    const base64Data = input.base64.replace(/^data:[^;]+;base64,/, "");
                    const buffer = Buffer.from(base64Data, "base64");
                    fs.writeFileSync(filePath, buffer);
              
                    const fileUrl = `/uploads/${filename}`;
                    return { url: fileUrl };
      };

      // const podcastId = await db
      //   .insert(podcast)
      //   .values({
      //     name: input.name,
      //     subdomain: input.subdomain,
      //     description: input.description,
      //     image: imageUrl,
      //     email: input.email,
      //     owner: input.owner,
      //     copyright: input.copyright,
      //     organizationId: ctx.org.id,
      //   })
      //   .returning({ id: podcast.id })
      //   .then((res) => res[0].id);

      // for (const s of input.seasons) {
      //   const seasonId = await db
      //     .insert(season)
      //     .values({
      //       name: s.name,
      //       description: s.description,
      //       image: s.image,
      //       podcastId,
      //     })
      //     .returning({ id: season.id })
      //     .then((res) => res[0].id);

      //   for (const e of s.episodes) {
      //     await db.insert(episode).values({
      //       name: e.name,
      //       description: e.description,
      //       image: e.image,
      //       audio: e.audio,
      //       duration: e.duration,
      //       seasonId,
      //     });
      //   }
      // }

      return { success: true };
    }),
});
