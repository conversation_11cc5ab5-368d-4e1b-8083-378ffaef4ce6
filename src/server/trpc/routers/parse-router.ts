import { TRPCError } from "@trpc/server";
import { XMLParser } from "fast-xml-parser";
import { z } from "zod";
import { createTRPCRouter, protectedOrgProcedure } from "../init";

interface Episode {
  name: string;
  description: string;
  image: string;
  audio: string;
  duration: string;
  seasonNumber?: number;
}

interface Season {
  name: string;
  description: string;
  image: string;
  episodes: Episode[];
}

function toArray<T>(maybeArray: T | T[] | undefined): T[] {
  if (!maybeArray) return [];
  return Array.isArray(maybeArray) ? maybeArray : [maybeArray];
}

export const parseRouter = createTRPCRouter({
  parseRSS: protectedOrgProcedure
    .input(z.object({ url: z.string().url() }))
    .mutation(async ({ input }) => {
      const res = await fetch(input.url);
      if (!res.ok) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Failed to fetch RSS feed",
        });
      }

      const xml = await res.text();

      // Configure parser to keep attributes (for enclosure url, itunes:image href, etc.)
      const parser = new XMLParser({
        ignoreAttributes: false,
        attributeNamePrefix: "@_",
        trimValues: true,
        // Let tag values be strings; CDATA is merged automatically.
        parseAttributeValue: false,
        parseTagValue: true,
        // Many feeds are lenient; this helps avoid hard failures.
        allowBooleanAttributes: true,
      });

      // biome-ignore lint/suspicious/noExplicitAny: we legitimately don't know the shape
      let parsed: any;
      try {
        parsed = parser.parse(xml);
        console.log(parsed);
      } catch {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Failed to parse RSS feed",
        });
      }

      // Expect RSS 2.0 structure: rss -> channel
      const channel = parsed?.rss?.channel;
      if (!channel) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Failed to find channel in RSS feed",
        });
      }

      const getElementText = (
        obj: Record<string, unknown> | null | undefined,
        key: string,
      ): string => (obj?.[key] ?? "") as string;

      // Namespaced helpers (e.g., "itunes:owner")
      const getNamespaced = (
        obj: Record<string, unknown> | null | undefined,
        nsKey: string,
      ): string =>
        (obj?.[nsKey] ?? obj?.[nsKey?.split(":")?.[1]] ?? "") as string;

      // Channel fields
      const channelImage =
        channel?.image?.url ?? channel?.["itunes:image"]?.["@_href"] ?? "";

      const itemsRaw = toArray(channel.item);
      // biome-ignore lint/suspicious/noExplicitAny: we legitimately don't know the shape
      const episodes: Episode[] = itemsRaw.map((item: any): Episode => {
        const audioUrl = item?.enclosure?.["@_url"] ?? "";
        const episodeImage =
          item?.["itunes:image"]?.["@_href"] ?? item?.image?.url ?? "";

        const seasonStr = getNamespaced(item, "itunes:season");
        const duration = getNamespaced(item, "itunes:duration");

        return {
          name: getElementText(item, "title"),
          description: getElementText(item, "description"),
          image: episodeImage,
          audio: audioUrl,
          duration: typeof duration === "string" ? duration : "",
          seasonNumber:
            typeof seasonStr === "string" && seasonStr
              ? Number.parseInt(seasonStr, 10)
              : undefined,
        };
      });

      // Group by season (if present)
      const seasonsMap = new Map<number, Episode[]>();
      const noSeasonEpisodes: Episode[] = [];

      for (const ep of episodes) {
        if (ep.seasonNumber != null && !Number.isNaN(ep.seasonNumber)) {
          if (!seasonsMap.has(ep.seasonNumber))
            seasonsMap.set(ep.seasonNumber, []);
          seasonsMap.get(ep.seasonNumber)?.push(ep);
        } else {
          noSeasonEpisodes.push(ep);
        }
      }

      const seasons: Season[] = Array.from(seasonsMap.entries())
        .sort(([a], [b]) => a - b)
        .map(([seasonNum, seasonEpisodes]) => ({
          name: `Season ${seasonNum}`,
          description: "",
          image: seasonEpisodes[0]?.image || "",
          episodes: seasonEpisodes,
        }));

      if (seasons.length === 0 && noSeasonEpisodes.length > 0) {
        seasons.push({
          name: "Season 1",
          description: "",
          image: noSeasonEpisodes[0]?.image || "",
          episodes: noSeasonEpisodes,
        });
      }

      const podcastData = {
        title: getElementText(channel, "title"),
        description: getElementText(channel, "description"),
        image: channelImage || "",
        email: getNamespaced(channel, "itunes:email"),
        owner:
          getNamespaced(channel, "itunes:owner") ||
          getNamespaced(channel, "itunes:author"),
        copyright: getElementText(channel, "copyright"),
        seasons,
        totalEpisodes: episodes.length,
      };

      if (!podcastData.email) {
        podcastData.email = getNamespaced(channel, "itunes:owner")[
          "itunes:email"
        ];
      }

      if (typeof podcastData.owner === "object") {
        podcastData.owner = getNamespaced(channel, "itunes:owner")[
          "itunes:name"
        ];
      }

      return { data: podcastData };
    }),
});
