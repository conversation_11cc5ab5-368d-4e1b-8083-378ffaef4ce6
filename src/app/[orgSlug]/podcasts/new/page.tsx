"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { $generateHtmlFromNodes } from "@lexical/html";
import { LinkNode } from "@lexical/link";
import { useMutation } from "@tanstack/react-query";
import { $isEditorState, createEditor } from "lexical";
import {
  ChevronDown,
  ChevronRight,
  Loader2,
  Trash2,
  Upload,
} from "lucide-react";
import type { NextPage } from "next";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { type UseFormReturn, useFieldArray, useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";
import { RTE } from "@/components/rte";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  InputGroup,
  InputGroupAddon,
  InputGroupInput,
  InputGroupText,
} from "@/components/ui/input-group";
import { Label } from "@/components/ui/label";
import { slugify } from "@/lib/utils";
import { useTRPC } from "@/server/trpc/client";

const isSerializedLexical = (v: unknown): v is { root: { type: string } } =>
  !!v && typeof v === "object" && (v as any).root?.type === "root";

// Convert a serialized Lexical state -> HTML
function serializedLexicalToHTML(serialized: any): string {
  const temp = createEditor({
    namespace: "html-export",
    nodes: [LinkNode], // add more nodes if your content uses them
  });
  const state = temp.parseEditorState(serialized);
  temp.setEditorState(state);
  let html = "";
  temp.getEditorState().read(() => {
    html = $generateHtmlFromNodes(temp);
  });
  return html;
}

// Convert live EditorState -> HTML
function editorStateToHTML(state: any): string {
  // safest is to go via .toJSON() then reuse the function above
  if (typeof state?.toJSON === "function") {
    return serializedLexicalToHTML(state.toJSON());
  }
  // fallback: do nothing
  return "";
}

// Normalize any description to a string (HTML if lexical; else leave as-is)
function normalizeDescription(desc: unknown): string | undefined {
  if ($isEditorState(desc)) return editorStateToHTML(desc);
  if (isSerializedLexical(desc)) return serializedLexicalToHTML(desc);
  if (typeof desc === "string") return desc; // already a string; keep it
  return undefined; // preserve undefined/null
}

const SerializedLexicalState = z
  .object({
    root: z.object({ type: z.literal("root") }).passthrough(),
  })
  .passthrough();

const DescriptionField = z
  .preprocess(
    (v) => {
      // 1) Live EditorState instance
      if ($isEditorState(v)) {
        try {
          return v.toJSON();
        } catch {
          // fall through to fail later
        }
      }

      // 2) JSON string of a serialized state
      if (typeof v === "string") {
        const s = v.trim();
        if (s.startsWith("{") || s.startsWith("[")) {
          try {
            const parsed = JSON.parse(s);
            if (
              parsed &&
              typeof parsed === "object" &&
              parsed.root?.type === "root"
            ) {
              return parsed; // validate as SerializedLexicalState below
            }
          } catch {
            // not JSON; treat as plain string
          }
        }
      }

      // 3) leave as-is (string or object) for the union validator
      return v;
    },
    z.union([z.string(), SerializedLexicalState]),
  )
  .optional();

const episodeSchema = z.object({
  name: z.string().min(1, "Episode name is required"),
  description: DescriptionField,
  image: z.string().url("Must be a valid URL").optional().or(z.literal("")),
  audio: z.string().url("Must be a valid URL").optional().or(z.literal("")),
  duration: z.string().optional(),
});

const seasonSchema = z.object({
  name: z.string().min(1, "Season name is required"),
  description: DescriptionField,
  image: z.string().url("Must be a valid URL").optional().or(z.literal("")),
  episodes: z.array(episodeSchema),
});

const podcastFormSchema = z.object({
  name: z.string().min(1, "Podcast name is required"),
  subdomain: z.string().optional(),
  description: DescriptionField,
  image: z.string().url("Must be a valid URL").optional().or(z.literal("")),
  email: z.string().email("Must be a valid email").optional().or(z.literal("")),
  owner: z.string().optional(),
  copyright: z.string().optional(),
  seasons: z.array(seasonSchema),
});

type PodcastFormValues = z.infer<typeof podcastFormSchema>;

const PodcastNewPage: NextPage = () => {
  const trpc = useTRPC();
  const router = useRouter();

  const [rssUrl, setRssUrl] = useState("");
  const [isImporting, setIsImporting] = useState(false);
  const [openSeasons, setOpenSeasons] = useState<Set<number>>(new Set());

  const { mutateAsync: parseRSS } = useMutation(
    trpc.parse.parseRSS.mutationOptions(),
  );

  const { mutateAsync: createPodcast } = useMutation(
    trpc.podcasts.create.mutationOptions(),
  );

  const form = useForm<PodcastFormValues>({
    resolver: zodResolver(podcastFormSchema),
    defaultValues: {
      name: "",
      subdomain: "",
      description: "",
      image: "",
      email: "",
      owner: "",
      copyright: "",
      seasons: [],
    },
  });

  const { fields: seasonFields, remove: removeSeason } = useFieldArray({
    control: form.control,
    name: "seasons",
  });

  const handleImportFromRss = async () => {
    if (!rssUrl.trim()) {
      toast.error("Please enter a valid RSS feed URL");
      return;
    }

    setIsImporting(true);
    try {
      const { data } = await parseRSS({ url: rssUrl }, {});

      form.reset({
        name: data.title || "",
        subdomain: slugify(data.title.toLowerCase()) || "",
        description: data.description || "",
        image: data.image || "",
        email: data.email || "",
        owner: data.owner || "",
        copyright: data.copyright || "",
        seasons: data.seasons || [],
      });

      if (data.seasons && data.seasons.length > 0) {
        setOpenSeasons(new Set([0]));
      }

      toast.success("RSS feed imported successfully");
      // setRssUrl("");
    } catch (error) {
      console.error(error);
      toast.error("Failed to import RSS feed");
    } finally {
      setIsImporting(false);
    }
  };

  const onSubmit = async (values: PodcastFormValues) => {
    // top-level
    if (values.description !== undefined) {
      const d = normalizeDescription(values.description);
      if (d !== undefined) values.description = d;
    }

    // seasons + episodes
    values.seasons = values.seasons.map((season) => {
      const seasonDesc = normalizeDescription(season.description);
      const episodes = season.episodes.map((ep) => {
        const epDesc = normalizeDescription(ep.description);
        return {
          ...ep,
          ...(epDesc !== undefined ? { description: epDesc } : {}),
        };
      });

      return {
        ...season,
        ...(seasonDesc !== undefined ? { description: seasonDesc } : {}),
        episodes,
      };
    });

    try {
      await createPodcast(values, {
        onSuccess: () => {
          toast.success("Podcast created successfully");
          // router.push("/podcasts");
        },
        onError: () => {
          toast.error("Failed to create podcast");
        },
      });
    } catch (error) {
      console.error(error);
      toast.error("Failed to create podcast");
    }

    console.log("Submitting podcast:", values);
    toast.info("Submitting podcast...");
  };

  const toggleSeason = (index: number) => {
    setOpenSeasons((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(index)) {
        newSet.delete(index);
      } else {
        newSet.add(index);
      }
      return newSet;
    });
  };

  return (
    <>
      <section className="flex items-end justify-between">
        <div className="space-y-4">
          <div className="space-y-2">
            <h1 className="text-4xl font-semibold tracking-tight">
              Create Podcast
            </h1>
            <p className="text-muted-foreground text-lg">
              Create a new podcast here. Or comfortably import by passing a feed
              URL.
            </p>
          </div>
        </div>
      </section>
      <section className="py-12">
        <div className="grid md:grid-cols-2 gap-12">
          <div className="space-y-2">
            <Label htmlFor="rss-url">RSS Feed URL</Label>
            <div className="flex gap-2">
              <Input
                id="rss-url"
                type="url"
                placeholder="https://example.com/podcast/feed.xml"
                value={rssUrl}
                onChange={(e) => setRssUrl(e.target.value)}
                className="flex-1"
              />
              <Button
                type="button"
                onClick={handleImportFromRss}
                disabled={isImporting}
              >
                {isImporting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Importing
                  </>
                ) : (
                  <>
                    <Upload className="mr-2 h-4 w-4" />
                    Import
                  </>
                )}
              </Button>
            </div>
            <p className="text-muted-foreground text-sm">
              Enter your podcast RSS feed URL to automatically populate the form
              fields
            </p>
          </div>
        </div>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="mt-6">
            <div className="grid md:grid-cols-2 gap-12">
              <div>
                <div className="sticky top-64 space-y-6">
                  <div className="grid gap-6 md:grid-cols-2 items-start">
                    <FormField
                      control={form.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            Podcast Name{" "}
                            <span className="text-destructive">*</span>
                          </FormLabel>
                          <FormControl>
                            <Input
                              placeholder="My Awesome Podcast"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="subdomain"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Subdomain</FormLabel>
                          <FormControl>
                            <InputGroup>
                              <InputGroupInput
                                placeholder="my-podcast"
                                {...field}
                              />
                              <InputGroupAddon align="inline-end">
                                <InputGroupText>.puka.cloud</InputGroupText>
                              </InputGroupAddon>
                            </InputGroup>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Description</FormLabel>
                        <FormControl>
                          <RTE
                            html={field.value ?? ""}
                            onChange={(html) => field.onChange(html)} // editor -> HTML
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="image"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Cover Image URL</FormLabel>
                        <FormControl>
                          <Input
                            type="url"
                            placeholder="https://example.com/cover.jpg"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid gap-6 md:grid-cols-3">
                    <FormField
                      control={form.control}
                      name="email"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Contact Email</FormLabel>
                          <FormControl>
                            <Input
                              type="email"
                              placeholder="<EMAIL>"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="owner"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Owner</FormLabel>
                          <FormControl>
                            <Input placeholder="John Doe" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="copyright"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Copyright</FormLabel>
                          <FormControl>
                            <Input placeholder="© 2025 My Podcast" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  <div className="flex justify-end gap-3 pt-4">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => router.back()}
                    >
                      Cancel
                    </Button>
                    <Button type="submit">Create Podcast</Button>
                  </div>
                </div>
              </div>

              <div className="space-y-6">
                {seasonFields.length > 0 && (
                  <div className="space-y-4 pt-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="text-lg font-semibold">
                          Seasons & Episodes
                        </h3>
                        <p className="text-sm text-muted-foreground">
                          {seasonFields.length} season
                          {seasonFields.length !== 1 ? "s" : ""} with{" "}
                          {seasonFields.reduce(
                            (acc, s) => acc + s.episodes.length,
                            0,
                          )}{" "}
                          episode
                          {seasonFields.reduce(
                            (acc, s) => acc + s.episodes.length,
                            0,
                          ) !== 1
                            ? "s"
                            : ""}
                        </p>
                      </div>
                    </div>

                    <div className="space-y-3">
                      {seasonFields.map((season, seasonIndex) => (
                        <SeasonFormSection
                          key={season.id}
                          seasonIndex={seasonIndex}
                          form={form}
                          isOpen={openSeasons.has(seasonIndex)}
                          onToggle={() => toggleSeason(seasonIndex)}
                          onRemove={() => removeSeason(seasonIndex)}
                        />
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </form>
        </Form>
      </section>
    </>
  );
};

export default PodcastNewPage;

function SeasonFormSection({
  seasonIndex,
  form,
  isOpen,
  onToggle,
  onRemove,
}: {
  seasonIndex: number;
  form: UseFormReturn<PodcastFormValues>;
  isOpen: boolean;
  onToggle: () => void;
  onRemove: () => void;
}) {
  const { fields: episodeFields, remove: removeEpisode } = useFieldArray({
    control: form.control,
    name: `seasons.${seasonIndex}.episodes`,
  });

  return (
    <Collapsible open={isOpen} onOpenChange={onToggle}>
      <Card className="py-0 overflow-hidden">
        <CollapsibleTrigger asChild>
          <CardHeader className="cursor-pointer hover:bg-muted transition-colors py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3 flex-1">
                {isOpen ? (
                  <ChevronDown className="h-5 w-5 text-muted-foreground flex-shrink-0" />
                ) : (
                  <ChevronRight className="h-5 w-5 text-muted-foreground flex-shrink-0" />
                )}
                {form.watch(`seasons.${seasonIndex}.image`) ? (
                  <div>
                    {/** biome-ignore lint/performance/noImgElement: gonna be really dynamic from where the images come so we dont need to optimize */}
                    <img
                      src={form.watch(`seasons.${seasonIndex}.image`) || ""}
                      alt={form.watch(`seasons.${seasonIndex}.name`) || ""}
                      width={64}
                      height={64}
                      className="object-cover rounded-md"
                    />
                  </div>
                ) : (
                  <div className="h-16 w-16 rounded-md bg-muted flex-shrink-0" />
                )}
                <div className="flex-1 min-w-0">
                  <CardTitle className="text-base truncate">
                    {form.watch(`seasons.${seasonIndex}.name`) ||
                      `Season ${seasonIndex + 1}`}
                  </CardTitle>
                  <CardDescription className="text-sm">
                    {episodeFields.length} episode
                    {episodeFields.length !== 1 ? "s" : ""}
                  </CardDescription>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant="secondary">{episodeFields.length}</Badge>
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  onClick={(e) => {
                    e.stopPropagation();
                    onRemove();
                  }}
                  className="h-8 w-8"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardHeader>
        </CollapsibleTrigger>
        <CollapsibleContent className="pb-4">
          <CardContent className="pt-0 space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <FormField
                control={form.control}
                name={`seasons.${seasonIndex}.name`}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Season Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Season 1" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name={`seasons.${seasonIndex}.image`}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Season Image URL</FormLabel>
                    <FormControl>
                      <Input
                        type="url"
                        placeholder="https://example.com/season.jpg"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name={`seasons.${seasonIndex}.description`}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Season Description</FormLabel>
                  <FormControl>
                    <RTE
                      html={field.value ?? ""}
                      onChange={(html) => field.onChange(html)}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="space-y-2 pt-2">
              <h4 className="text-sm font-semibold">Episodes</h4>
              {episodeFields.map((episode, episodeIndex) => (
                <EpisodeFormSection
                  key={episode.id}
                  seasonIndex={seasonIndex}
                  episodeIndex={episodeIndex}
                  form={form}
                  onRemove={() => removeEpisode(episodeIndex)}
                />
              ))}
            </div>
          </CardContent>
        </CollapsibleContent>
      </Card>
    </Collapsible>
  );
}

function EpisodeFormSection({
  seasonIndex,
  episodeIndex,
  form,
  onRemove,
}: {
  seasonIndex: number;
  episodeIndex: number;
  form: UseFormReturn<PodcastFormValues>;
  onRemove: () => void;
}) {
  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <Card className="bg-muted/30 py-0">
      <CardHeader className="py-4 gap-0">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2 flex-1 min-w-0">
            <Button
              type="button"
              variant="ghost"
              size="icon"
              onClick={() => setIsExpanded(!isExpanded)}
              className="h-6 w-6 flex-shrink-0"
            >
              {isExpanded ? (
                <ChevronDown className="h-4 w-4" />
              ) : (
                <ChevronRight className="h-4 w-4" />
              )}
            </Button>
            {form.watch(
              `seasons.${seasonIndex}.episodes.${episodeIndex}.image`,
            ) ? (
              <div>
                {/** biome-ignore lint/performance/noImgElement: gonna be really dynamic from where the images come so we dont need to optimize */}
                <img
                  src={
                    form.watch(
                      `seasons.${seasonIndex}.episodes.${episodeIndex}.image`,
                    ) || ""
                  }
                  alt={
                    form.watch(
                      `seasons.${seasonIndex}.episodes.${episodeIndex}.name`,
                    ) || ""
                  }
                  width={32}
                  height={32}
                  className="object-cover rounded-md"
                />
              </div>
            ) : (
              <div className="h-8 w-8 rounded-md bg-muted flex-shrink-0" />
            )}
            <span className="text-sm font-medium truncate">
              {form.watch(
                `seasons.${seasonIndex}.episodes.${episodeIndex}.name`,
              ) || `Episode ${episodeIndex + 1}`}
            </span>
          </div>
          <Button
            type="button"
            variant="ghost"
            size="icon"
            onClick={onRemove}
            className="h-6 w-6 flex-shrink-0"
          >
            <Trash2 className="h-3 w-3" />
          </Button>
        </div>
      </CardHeader>
      {isExpanded && (
        <CardContent className="pt-0 pb-3 space-y-3">
          <FormField
            control={form.control}
            name={`seasons.${seasonIndex}.episodes.${episodeIndex}.name`}
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-xs">Episode Name</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Episode title"
                    {...field}
                    className="h-8 text-sm"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name={`seasons.${seasonIndex}.episodes.${episodeIndex}.description`}
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-xs">Description</FormLabel>
                <FormControl>
                  <RTE
                    html={field.value ?? ""}
                    onChange={(html) => field.onChange(html)}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="grid gap-3 md:grid-cols-2">
            <FormField
              control={form.control}
              name={`seasons.${seasonIndex}.episodes.${episodeIndex}.image`}
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-xs">Image URL</FormLabel>
                  <FormControl>
                    <Input
                      type="url"
                      placeholder="https://..."
                      {...field}
                      className="h-8 text-sm"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name={`seasons.${seasonIndex}.episodes.${episodeIndex}.duration`}
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-xs">Duration</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="45:30"
                      {...field}
                      className="h-8 text-sm"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name={`seasons.${seasonIndex}.episodes.${episodeIndex}.audio`}
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-xs">Audio URL</FormLabel>
                <FormControl>
                  <Input
                    type="url"
                    placeholder="https://example.com/episode.mp3"
                    {...field}
                    className="h-8 text-sm"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </CardContent>
      )}
    </Card>
  );
}
