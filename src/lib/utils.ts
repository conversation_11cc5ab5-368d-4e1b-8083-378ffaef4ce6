import { type ClassValue, clsx } from "clsx";
import type { SerializedEditorState } from "lexical";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function slugify(
  input: unknown,
  opts?: {
    separator?: "-" | "_";
    maxLength?: number;
    fallback?: string;
    lowercase?: boolean;
    keepUnicodeLetters?: boolean;
  },
): string {
  const {
    separator = "-",
    maxLength,
    fallback = "n-a",
    lowercase = true,
    keepUnicodeLetters = true,
  } = opts ?? {};

  let s = String(input ?? "");

  s = s.trim();

  if (lowercase) s = s.toLowerCase();

  s = s.normalize("NFKD").replace(/[\u0300-\u036f]/g, "");

  const allowedRegex = keepUnicodeLetters
    ? /[^\p{L}\p{N}\-_]+/gu
    : /[^a-z0-9\-_]+/g;

  s = s.replace(allowedRegex, separator);

  s = s.replace(/[-_]{2,}/g, separator);

  s = s.replace(/^[-_]+|[-_]+$/g, "");

  if (typeof maxLength === "number" && maxLength > 0) {
    if (s.length > maxLength) {
      s = s.slice(0, maxLength);

      s = s.replace(/^[-_]+|[-_]+$/g, "");
    }
  }

  if (!s) return fallback;

  if (s[0] === "-" || s[0] === "_") s = s.slice(1);
  if (!s) return fallback;
  if (s[s.length - 1] === "-" || s[s.length - 1] === "_") s = s.slice(0, -1);
  if (!s) return fallback;

  return s;
}
